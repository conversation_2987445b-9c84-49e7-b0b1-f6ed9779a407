<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark - 游戏选择</title>
    <meta name="description" content="捕捉决定性瞬间，引燃无限可能。选择您喜欢的游戏开始体验！">
    <meta name="keywords" content="游戏,休闲游戏,策略游戏,音乐游戏,时空织梦者,瞬光捕手,量子共鸣者">
    <meta name="author" content="Split-Second Spark Team">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/main.js" as="script">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/game-selector.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    
    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="screen active">
        <div class="loading-container">
            <div class="spark-logo">
                <div class="spark-core"></div>
                <div class="spark-rays">
                    <div class="ray"></div>
                    <div class="ray"></div>
                    <div class="ray"></div>
                    <div class="ray"></div>
                </div>
            </div>
            <h1 class="main-title">Split-Second Spark</h1>
            <p class="main-subtitle" data-i18n="loading.subtitle">捕捉决定性瞬间，引燃无限可能</p>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="loading-progress-fill"></div>
                </div>
                <div class="loading-text" id="loading-text" data-i18n="loading.initializing">正在初始化...</div>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="main-screen" class="screen">
        <div class="main-container">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-content">
                    <div class="logo-section">
                        <div class="main-logo">
                            <div class="logo-spark">
                                <div class="spark-core"></div>
                                <div class="spark-rays">
                                    <div class="ray"></div>
                                    <div class="ray"></div>
                                    <div class="ray"></div>
                                    <div class="ray"></div>
                                </div>
                            </div>
                            <div class="logo-text">
                                <h1 class="title">Split-Second Spark</h1>
                                <p class="subtitle" data-i18n="main.subtitle">捕捉决定性瞬间，引燃无限可能</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 语言切换和设置 -->
                    <div class="header-controls">
                        <button class="control-btn language-btn" id="language-toggle" title="切换语言">
                            <span class="btn-icon">🌐</span>
                            <span class="btn-text" data-i18n="controls.language">中文</span>
                        </button>
                        <button class="control-btn settings-btn" id="settings-btn" title="设置">
                            <span class="btn-icon">⚙️</span>
                            <span class="btn-text" data-i18n="controls.settings">设置</span>
                        </button>
                    </div>
                </div>
            </header>

            <!-- 游戏选择区域 -->
            <main class="games-section">
                <div class="section-header">
                    <h2 class="section-title" data-i18n="games.title">选择游戏</h2>
                    <p class="section-description" data-i18n="games.description">每个游戏都有独特的玩法和挑战，选择您感兴趣的开始体验</p>
                </div>

                <div class="games-grid" id="games-grid">
                    <!-- 时空织梦者 -->
                    <div class="game-card" data-game="temporal-dream-weaver">
                        <div class="card-background">
                            <div class="card-gradient temporal-gradient"></div>
                            <div class="card-particles">
                                <div class="particle"></div>
                                <div class="particle"></div>
                                <div class="particle"></div>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <div class="game-icon temporal-icon">
                                <div class="time-symbol">⏰</div>
                                <div class="dream-waves">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>
                            
                            <div class="game-info">
                                <h3 class="game-title" data-i18n="games.temporal.title">时空织梦者</h3>
                                <p class="game-subtitle" data-i18n="games.temporal.subtitle">Temporal Dream Weaver</p>
                                <p class="game-description" data-i18n="games.temporal.description">
                                    通过操控时间流动来编织梦境，解决复杂的时空谜题。注重策略思考和时间管理的创新解谜游戏。
                                </p>
                                
                                <div class="game-features">
                                    <span class="feature-tag" data-i18n="games.temporal.feature1">时间操控</span>
                                    <span class="feature-tag" data-i18n="games.temporal.feature2">策略解谜</span>
                                    <span class="feature-tag" data-i18n="games.temporal.feature3">梦境编织</span>
                                </div>
                            </div>
                            
                            <div class="card-actions">
                                <button class="play-btn" data-action="play" data-game="temporal-dream-weaver">
                                    <span class="btn-icon">🎮</span>
                                    <span class="btn-text" data-i18n="actions.play">开始游戏</span>
                                </button>
                                <button class="preview-btn" data-action="preview" data-game="temporal-dream-weaver">
                                    <span class="btn-icon">👁️</span>
                                    <span class="btn-text" data-i18n="actions.preview">预览</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 瞬光捕手 -->
                    <div class="game-card" data-game="split-second-spark">
                        <div class="card-background">
                            <div class="card-gradient spark-gradient"></div>
                            <div class="card-sparks">
                                <div class="spark"></div>
                                <div class="spark"></div>
                                <div class="spark"></div>
                                <div class="spark"></div>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <div class="game-icon spark-icon">
                                <div class="spark-symbol">⚡</div>
                                <div class="spark-rings">
                                    <div class="ring"></div>
                                    <div class="ring"></div>
                                </div>
                            </div>
                            
                            <div class="game-info">
                                <h3 class="game-title" data-i18n="games.spark.title">瞬光捕手</h3>
                                <p class="game-subtitle" data-i18n="games.spark.subtitle">Split-Second Spark</p>
                                <p class="game-description" data-i18n="games.spark.description">
                                    考验反应速度和时机把握的休闲游戏。在光点达到最佳时机时精准点击，获得高分并解锁更多关卡。
                                </p>
                                
                                <div class="game-features">
                                    <span class="feature-tag" data-i18n="games.spark.feature1">精准时机</span>
                                    <span class="feature-tag" data-i18n="games.spark.feature2">连击系统</span>
                                    <span class="feature-tag" data-i18n="games.spark.feature3">反应挑战</span>
                                </div>
                            </div>
                            
                            <div class="card-actions">
                                <button class="play-btn" data-action="play" data-game="split-second-spark">
                                    <span class="btn-icon">🎮</span>
                                    <span class="btn-text" data-i18n="actions.play">开始游戏</span>
                                </button>
                                <button class="preview-btn" data-action="preview" data-game="split-second-spark">
                                    <span class="btn-icon">👁️</span>
                                    <span class="btn-text" data-i18n="actions.preview">预览</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 量子共鸣者 -->
                    <div class="game-card" data-game="quantum-resonance">
                        <div class="card-background">
                            <div class="card-gradient quantum-gradient"></div>
                            <div class="card-quantum">
                                <div class="quantum-orbit">
                                    <div class="electron"></div>
                                    <div class="electron"></div>
                                    <div class="electron"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <div class="game-icon quantum-icon">
                                <div class="quantum-symbol">🌌</div>
                                <div class="resonance-waves">
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                    <div class="wave"></div>
                                </div>
                            </div>
                            
                            <div class="game-info">
                                <h3 class="game-title" data-i18n="games.quantum.title">量子共鸣者</h3>
                                <p class="game-subtitle" data-i18n="games.quantum.subtitle">Quantum Resonance</p>
                                <p class="game-description" data-i18n="games.quantum.description">
                                    音乐节奏与物理模拟结合的创新游戏。通过控制量子粒子的共鸣频率创建连锁反应，体验独特的量子物理机制。
                                </p>
                                
                                <div class="game-features">
                                    <span class="feature-tag" data-i18n="games.quantum.feature1">量子共鸣</span>
                                    <span class="feature-tag" data-i18n="games.quantum.feature2">音乐节奏</span>
                                    <span class="feature-tag" data-i18n="games.quantum.feature3">物理模拟</span>
                                </div>
                            </div>
                            
                            <div class="card-actions">
                                <button class="play-btn" data-action="play" data-game="quantum-resonance">
                                    <span class="btn-icon">🎮</span>
                                    <span class="btn-text" data-i18n="actions.play">开始游戏</span>
                                </button>
                                <button class="preview-btn" data-action="preview" data-game="quantum-resonance">
                                    <span class="btn-icon">👁️</span>
                                    <span class="btn-text" data-i18n="actions.preview">预览</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            <!-- 底部信息 -->
            <footer class="main-footer">
                <div class="footer-content">
                    <div class="footer-info">
                        <p class="copyright" data-i18n="footer.copyright">© 2024 Split-Second Spark Team. All rights reserved.</p>
                        <p class="version" data-i18n="footer.version">版本 1.0.0</p>
                    </div>
                    <div class="footer-links">
                        <a href="#" class="footer-link" data-i18n="footer.help">帮助</a>
                        <a href="#" class="footer-link" data-i18n="footer.about">关于</a>
                        <a href="#" class="footer-link" data-i18n="footer.feedback">反馈</a>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- 游戏预览模态框 -->
    <div id="preview-modal" class="modal">
        <div class="modal-overlay" id="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="preview-title">游戏预览</h3>
                <button class="modal-close" id="modal-close">×</button>
            </div>
            <div class="modal-body" id="preview-content">
                <!-- 预览内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="preview-close" data-i18n="actions.close">关闭</button>
                <button class="btn btn-primary" id="preview-play" data-i18n="actions.play">开始游戏</button>
            </div>
        </div>
    </div>

    <!-- 设置模态框 -->
    <div id="settings-modal" class="modal">
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" data-i18n="settings.title">设置</h3>
                <button class="modal-close">×</button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h4 class="settings-section-title" data-i18n="settings.display">显示设置</h4>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.theme">主题</label>
                        <select class="setting-select" id="theme-select">
                            <option value="dark" data-i18n="settings.theme.dark">深色</option>
                            <option value="light" data-i18n="settings.theme.light">浅色</option>
                            <option value="auto" data-i18n="settings.theme.auto">自动</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.language">语言</label>
                        <select class="setting-select" id="language-select">
                            <option value="zh-CN">中文</option>
                            <option value="en-US">English</option>
                        </select>
                    </div>
                </div>

                <div class="settings-section">
                    <h4 class="settings-section-title" data-i18n="settings.performance">性能设置</h4>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.effects">视觉效果</label>
                        <select class="setting-select" id="effects-select">
                            <option value="high" data-i18n="settings.effects.high">高</option>
                            <option value="medium" data-i18n="settings.effects.medium">中</option>
                            <option value="low" data-i18n="settings.effects.low">低</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.fps">帧率限制</label>
                        <select class="setting-select" id="fps-select">
                            <option value="60">60 FPS</option>
                            <option value="30">30 FPS</option>
                            <option value="auto" data-i18n="settings.fps.auto">自动</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" data-i18n="actions.cancel">取消</button>
                <button class="btn btn-primary" data-i18n="actions.save">保存</button>
            </div>
        </div>
    </div>

    <!-- JavaScript 模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/game-launcher.js"></script>
    <script src="js/ui/modal-manager.js"></script>
    <script src="js/ui/settings-manager.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
